{"cells": [{"cell_type": "code", "execution_count": 13, "id": "81067bee", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "import json\n", "import re\n", "from pprint import pprint"]}, {"cell_type": "code", "execution_count": 3, "id": "ef0ab66c", "metadata": {}, "outputs": [], "source": ["client = OpenAI(\n", "  api_key=\"************************************************************************************\",\n", "  base_url=\"https://api.x.ai/v1\",\n", ")"]}, {"cell_type": "code", "execution_count": 28, "id": "33e30c1d", "metadata": {}, "outputs": [], "source": ["pathSystemPrompt = r\"resource\\CreditSystemPrompt_V1.txt\"\n", "pathUserContent = r\"D:\\Projects\\USBankDetailExtraction\\Data1\\20250624_155437_5b8a0b6b\\PDFs\\MultiPageResponses\\CreditPages\\CreditPages_page_3\\CreditPages_page_3_strUserPrompt.txt\"\n", "pathResponseFormat = r\"D:\\Projects\\USBankDetailExtraction\\resource\\ResponseFormat_V1_grok.json\"\n", "\n", "# -------- LOAD FILES --------\n", "def load_file_content(file_path):\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return f.read()\n", "\n", "def load_json_content(file_path):\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        return json.load(f)\n", "    \n", "strSystemPrompt = load_file_content(pathSystemPrompt)\n", "strUserContent = load_file_content(pathUserContent)\n", "strResponseFormat = load_json_content(pathResponseFormat).get(\"creditInfo\")    "]}, {"cell_type": "code", "execution_count": 29, "id": "ab45aa4f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are a completely and perfectly obedient US accountant who is an expert at structured data extraction from  bank statement. Follow the below steps to perform the complete task:\n", "\n", "Step 1:\n", "The conversion of a PDF to a text  bank statement is provided in UserContent in unstructured form. Analyze UserContent completely that is given in the following csv type structure in triple quotes.\n", "'''\n", "Page No., Text, X1, Y1, X2, Y2\n", "[ActualPageNumber], [ActualText], [x1], [y1], [x2], [y2]\n", "\n", "Table-[TableNo]\n", "[Heading1], [Heading2], ... , [HeadingN]\n", "[Cell1], [Cell2], ... , [CellN]\n", "'''\n", "Here x1, y1, x2, y2 represents standard bounding box coordinates of actual text.\n", "\n", "Step 2:\n", "Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly.\n", "\n", "Step 3:\n", "Find relevant information from the reconstructed layout and fill out the required output file in the given response format structure. If something is not found in the reconstructed layout, keep the respective value of that field as ''.\n", "\n", "Step 4:\n", "Ensure that each data point is assigned to the most appropriate category and isn't counted more than once.\n", "\n", "Step 5:\n", "Extract only all the information from the 'Deposits and Other Credits' section table (exmaple description will be 'MERCH SETL EPX ST 034671034' or 'Deposit/Credit' or any other). Ensure accuracy in capturing the date, description, and amount. Preserve the exact numerical values without any modifications or rounding. Maintain formatting for readability.\n", "\n", "CRITICAL - HANDLING MULTI-LINE DESCRIPTIONS:\n", "1. Pay special attention to transaction descriptions that span multiple lines in the original document\n", "2. For descriptions that continue on the next line (like \"MERCH SETL EPX ST 034671034\" on one line and \"CCD\" on the next), combine all parts into a single complete description\n", "3. Use the bounding box coordinates to determine if text on adjacent lines is part of the same description\n", "4. Look for transaction type codes (like CCD, PPD, WEB) that often appear on a separate line below the main description\n", "5. Always include transaction type codes (CCD, PPD, WEB, etc.) as part of the description, even if they appear on a separate line\n", "6. Check for vertical alignment and proximity of text elements to identify multi-line descriptions\n", "\n", "Example of proper extraction:\n", "Original PDF layout:\n", "```\n", "8/20    MERCH SETL EPX ST 034671034       3.77-\n", "        CCD\n", "```\n", "\n", "Correct extraction: \"MERCH SETL EPX ST 034671034 CCD\"\n", "Incorrect extraction: \"MERCH SETL EPX ST 034671034\" (missing the CCD part)\n"]}], "source": ["print(strSystemPrompt)"]}, {"cell_type": "code", "execution_count": 30, "id": "55a57dc0", "metadata": {}, "outputs": [], "source": ["# -------- BUILD MESSAGES --------\n", "messages = [\n", "    {\"role\": \"system\", \"content\": strSystemPrompt},\n", "    {\"role\": \"user\", \"content\": f\"{strUserContent}\\n\\nRespond strictly in this JSON format:\\n{strResponseFormat}\"}\n", "]\n", "\n", "\n", "response = client.chat.completions.create(\n", "        model=\"grok-3-mini\",\n", "        reasoning_effort=\"high\",\n", "        messages=messages,\n", ")"]}, {"cell_type": "code", "execution_count": 31, "id": "d9b23fcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('{\\n'\n", " '  \"CreditsInfo\": [\\n'\n", " '    {\\n'\n", " '      \"Date\": \"11/01\",\\n'\n", " '      \"Description\": \"MERCH SETL EPX ST 034671034 CCD\",\\n'\n", " '      \"Amount\": 3896.65\\n'\n", " '    },\\n'\n", " '    {\\n'\n", " '      \"Date\": \"11/01\",\\n'\n", " '      \"Description\": \"Deposit/Credit\",\\n'\n", " '      \"Amount\": 17533.64\\n'\n", " '    },\\n'\n", " '    {\\n'\n", " '      \"Date\": \"11/04\",\\n'\n", " '      \"Description\": \"MERCH SETL EPX ST 034671034 CCD\",\\n'\n", " '      \"Amount\": 2816.48\\n'\n", " '    },\\n'\n", " '    {\\n'\n", " '      \"Date\": \"11/04\",\\n'\n", " '      \"Description\": \"MERCH SETL EPX ST 034671034 CCD\",\\n'\n", " '      \"Amount\": 2689.61\\n'\n", " '    },\\n'\n", " '    {\\n'\n", " '      \"Date\": \"11/04\",\\n'\n", " '      \"Description\": \"MERCH SETL EPX ST 034671034 CCD\",\\n'\n", " '      \"Amount\": 1538.06\\n'\n", " '    },\\n'\n", " '    {\\n'\n", " '      \"Date\": \"11/04\",\\n'\n", " '      \"Description\": \"Deposit/Credit\",\\n'\n", " '      \"Amount\": 13148.38\\n'\n", " '    }\\n'\n", " '  ],\\n'\n", " '  \"Previous Balannce\": 433171.68,\\n'\n", " '  \"StatementStartData\": \"\",\\n'\n", " '  \"StatementEndDate\": \"\"\\n'\n", " '}')\n"]}], "source": ["pprint(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 23, "id": "a643339c", "metadata": {}, "outputs": [], "source": ["object = client.chat.completions.create(\n", "    model=\"grok-3-mini\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": strSystemPrompt},\n", "        {\"role\": \"user\", \"content\": strUserContent}\n", "    ],\n", "    response_format=strResponseFormat.get(\"format\"),\n", "    seed=33,\n", "    temperature=0,\n", "    reasoning_effort=\"high\"\n", ")"]}, {"cell_type": "code", "execution_count": 25, "id": "9df62a43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"CreditsInfo\": [\n", "    {\n", "      \"Date\": \"11/01\",\n", "      \"Description\": \"Deposit/Credit\",\n", "      \"Amount\": 17533.64\n", "    },\n", "    {\n", "      \"Date\": \"11/04\",\n", "      \"Description\": \"Deposit/Credit\",\n", "      \"Amount\": 13148.38\n", "    }\n", "  ],\n", "  \"Previous Balannce\": 433171.68,\n", "  \"StatementStartData\": \"\",\n", "  \"StatementEndDate\": \"\"\n", "}\n"]}], "source": ["print(object.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 20, "id": "58845b23", "metadata": {}, "outputs": [], "source": ["strResponseFormat.get(\"creditInfo\")"]}, {"cell_type": "code", "execution_count": 22, "id": "0f1d969f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'type': 'json_schema',\n", " 'json_schema': {'name': 'ExtractCreditsInfo',\n", "  'description': 'Schema for extracting deposit and credit transaction details from a US bank statement.',\n", "  'schema': {'type': 'object',\n", "   'properties': {'CreditsInfo': {'type': 'array',\n", "     'description': 'List of all deposit and credit transactions extracted from the document.',\n", "     'items': {'type': 'object',\n", "      'properties': {'Date': {'type': 'string',\n", "        'description': 'The date of the credit transaction in MM/DD format.'},\n", "       'Description': {'type': 'string',\n", "        'description': 'A brief description of the transaction, including the source or type of deposit.'},\n", "       'Amount': {'type': 'number',\n", "        'description': 'The exact credited amount without any modifications or rounding.'}},\n", "      'required': ['Date', 'Description', 'Amount'],\n", "      'additionalProperties': False}},\n", "    'Previous Balannce': {'type': 'number',\n", "     'description': 'Extract the previous balance from the First Page'},\n", "    'StatementStartData': {'type': 'string',\n", "     'description': 'Extract the Statement Start Data'},\n", "    'StatementEndDate': {'type': 'string',\n", "     'description': 'Extract the Statement End Data'}},\n", "   'required': ['CreditsInfo',\n", "    'Previous Balannce',\n", "    'StatementStartData',\n", "    'StatementEndDate'],\n", "   'additionalProperties': False},\n", "  'strict': True}}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["strResponseFormat.get(\"format\")"]}, {"cell_type": "code", "execution_count": null, "id": "15aba4b4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}